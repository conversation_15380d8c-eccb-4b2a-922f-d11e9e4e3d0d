<?php
/**
 * Storage Link Creator for Shared Hosting
 * Upload this file to your server and run it once after deployment
 */

echo "Creating storage link for shared hosting...\n";

$publicStoragePath = __DIR__ . '/public/storage';
$storagePath = __DIR__ . '/storage/app/public';

// Remove existing link/directory
if (is_link($publicStoragePath)) {
    unlink($publicStoragePath);
} elseif (is_dir($publicStoragePath)) {
    function removeDirectory($dir) {
        if (!is_dir($dir)) return;
        $files = array_diff(scandir($dir), array('.','..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
    removeDirectory($publicStoragePath);
}

// Try to create symlink
if (function_exists('symlink') && symlink($storagePath, $publicStoragePath)) {
    echo "✓ Symlink created successfully\n";
} else {
    // Fallback: copy files
    function copyDirectory($src, $dst) {
        if (!is_dir($src)) return false;
        if (!is_dir($dst)) mkdir($dst, 0755, true);
        
        $dir = opendir($src);
        while (($file = readdir($dir)) !== false) {
            if ($file != '.' && $file != '..') {
                $srcFile = $src . '/' . $file;
                $dstFile = $dst . '/' . $file;
                
                if (is_dir($srcFile)) {
                    copyDirectory($srcFile, $dstFile);
                } else {
                    copy($srcFile, $dstFile);
                }
            }
        }
        closedir($dir);
        return true;
    }
    
    if (copyDirectory($storagePath, $publicStoragePath)) {
        echo "✓ Files copied successfully (symlink not supported)\n";
    } else {
        echo "✗ Failed to create storage link\n";
    }
}

echo "Storage link setup complete!\n";
?>