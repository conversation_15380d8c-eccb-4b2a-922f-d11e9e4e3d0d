# Quick Deployment Summary - www.pandapatronage.com

## 🎯 Target Configuration
- **Domain**: www.pandapatronage.com
- **Hostinger Path**: `/public_html/`
- **Document Root**: `/public_html/` (Fixed - cannot be changed in shared hosting)

## ✅ Preparation Complete
- ✓ Production dependencies installed
- ✓ Frontend assets built (`public/build/`)
- ✓ Application key generated
- ✓ Production .env file created
- ✓ .htaccess files prepared
- ✓ Storage link script ready

## 📦 Files to Upload

### 1. Laravel Core Files
Upload ALL these directories/files to `/public_html/`:
```
app/
bootstrap/
config/
database/
resources/
routes/
storage/
vendor/          ← With production dependencies only
artisan
composer.json
composer.lock
```

### 1a. Public Directory Contents
Upload contents of `public/` directory directly to `/public_html/` root:
```
index.php        ← Update paths to point to Laravel core
.htaccess        ← From public.htaccess
build/           ← Frontend assets
favicon.ico
robots.txt
(any other public assets)
```

### 2. Deployment-Specific Files
Copy from `deployment/` folder:
```
.env.production → upload as .env to /public_html/
index.php → upload to /public_html/ (updated with correct paths)
public.htaccess → upload as .htaccess to /public_html/
create_storage_link.php → upload to /public_html/
```

### 3. DO NOT Upload
- `node_modules/`
- `.git/`
- `tests/`
- `deployment/`
- Development `.env`
- `*.md` files

## 🔧 Hostinger Setup

### 1. Document Root (Shared Hosting Limitation)
- **Cannot change document root** in shared hosting
- Document root is fixed to `/public_html/`
- This is why we upload public/ contents directly to public_html/

### 2. PHP Version
- Ensure PHP 8.2+ is selected

### 3. Database
- Create MySQL database in control panel
- Update credentials in uploaded `.env` file

## 🚀 Post-Upload Steps

### 1. Set Permissions (via File Manager or SSH)
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### 2. Create Storage Link
Visit: `https://www.pandapatronage.com/create_storage_link.php`
(This will create the storage symlink)

### 3. Run Migrations (if SSH available)
```bash
php artisan migrate --force
```

### 4. Cache for Production (if SSH available)
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🧪 Testing Checklist
- [ ] Homepage loads: `https://www.pandapatronage.com`
- [ ] CSS/JS assets loading correctly
- [ ] Images from storage folder accessible
- [ ] Contact forms working
- [ ] Admin panel accessible
- [ ] Database connections working

## 🆘 Quick Troubleshooting

| Issue | Solution |
|-------|----------|
| 500 Error | Check file permissions, verify .env settings |
| Images not loading | Run storage link script |
| CSS/JS missing | Check if `public/build/` uploaded correctly |
| Database error | Verify database credentials in .env |

## 📞 Support
- **Hostinger Docs**: https://support.hostinger.com
- **Laravel Docs**: https://laravel.com/docs
- **Project Logs**: Check `storage/logs/` for errors

---
**Generated on**: July 24, 2025
**App Key**: `base64:fDcDxdmV9fOj6K3+V9V0whZuVWXmol9HRd+5zRCQzu8=`
