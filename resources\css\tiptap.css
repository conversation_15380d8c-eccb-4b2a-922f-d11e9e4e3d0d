/* Tiptap Editor Styles */
.tiptap-editor {
    display: flex;
    flex-direction: column;
}

.tiptap-content-wrapper {
    min-height: 250px;
    flex: 1;
    overflow: visible;
}

.tiptap-editor .ProseMirror-focused {
    outline: none;
}

.tiptap-editor .ProseMirror {
    outline: none;
    border: none;
    padding: 1rem;
    min-height: 250px;
    height: auto;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    resize: none;
    overflow: visible;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

.tiptap-editor .ProseMirror h1 {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.2;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h2 {
    font-size: 1.75rem;
    font-weight: 600;
    line-height: 1.3;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h4 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h5 {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h6 {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.tiptap-editor .ProseMirror ul li {
    list-style-type: disc;
    margin-bottom: 0.25rem;
}

.tiptap-editor .ProseMirror ol li {
    list-style-type: decimal;
    margin-bottom: 0.25rem;
}

.tiptap-editor .ProseMirror blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6b7280;
}

.tiptap-editor .ProseMirror code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.875rem;
}

.tiptap-editor .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

.tiptap-editor .ProseMirror a:hover {
    color: #1d4ed8;
}

.tiptap-editor .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2rem 0;
}

.tiptap-editor .ProseMirror strong {
    font-weight: 700;
}

.tiptap-editor .ProseMirror em {
    font-style: italic;
}

.tiptap-editor .ProseMirror u {
    text-decoration: underline;
}

.tiptap-editor .ProseMirror s {
    text-decoration: line-through;
}

/* Text alignment */
.tiptap-editor .ProseMirror [data-text-align="left"] {
    text-align: left;
}

.tiptap-editor .ProseMirror [data-text-align="center"] {
    text-align: center;
}

.tiptap-editor .ProseMirror [data-text-align="right"] {
    text-align: right;
}

/* Focus styles */
.tiptap-editor .ProseMirror:focus {
    outline: none;
}

.tiptap-editor:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
}

/* Ensure proper content expansion */
.tiptap-editor .ProseMirror > * {
    margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror > *:last-child {
    margin-bottom: 0;
}

/* Handle long content properly */
.tiptap-editor .ProseMirror {
    white-space: pre-wrap;
    line-height: 1.6;
}

/* Ensure the editor expands with content */
.tiptap-editor {
    height: auto;
    overflow: visible;
}

.tiptap-content-wrapper {
    height: auto;
    overflow: visible;
}

/* Selection styles */
.tiptap-editor .ProseMirror ::selection {
    background-color: #3b82f6;
    color: white;
}

.tiptap-editor .ProseMirror ::-moz-selection {
    background-color: #3b82f6;
    color: white;
}

/* Cursor styles for better visibility */
.tiptap-editor .ProseMirror {
    caret-color: #3b82f6;
}
