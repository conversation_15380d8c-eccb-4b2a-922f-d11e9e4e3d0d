<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Article;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ArticleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $articles = Article::latest()->paginate(10);
        return inertia('Admin/Articles/Index', compact('articles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return inertia('Admin/Articles/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'tag' => 'required|string|max:255',
            'content' => 'required|string',
            'writer' => 'required|array',
            'writer.name' => 'required|string|max:255',
            'writer.bio' => 'nullable|string|max:500',
            'writer.avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'read_time' => 'nullable|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('articles', 'public');
            $validated['image'] = '/storage/' . $imagePath;
        }

        // Handle author image upload
        if ($request->hasFile('writer.avatar')) {
            $avatarPath = $request->file('writer.avatar')->store('articles/authors', 'public');
            $validated['writer']['avatar'] = '/storage/' . $avatarPath;
        }

        // Handle featured articles limit (max 2)
        if ($validated['is_featured']) {
            $featuredCount = Article::where('is_featured', true)->count();
            if ($featuredCount >= 2) {
                // Unfeature the oldest featured article
                Article::where('is_featured', true)
                    ->oldest()
                    ->first()
                    ->update(['is_featured' => false]);
            }
        }

        // Convert content string to array format for consistency
        if (isset($validated['content']) && is_string($validated['content'])) {
            $validated['content'] = $this->convertContentToArray($validated['content']);
        }

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);
        $validated['date'] = now()->format('M d, Y');

        Article::create($validated);

        return redirect()->route('admin.articles.index')->with('success', 'Article created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Article $article)
    {
        return inertia('Admin/Articles/Show', compact('article'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Article $article)
    {
        return inertia('Admin/Articles/Edit', compact('article'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Article $article)
    {
        // Base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'tag' => 'required|string|max:255',
            'content' => 'required|string',
            'writer' => 'required|array',
            'writer.name' => 'required|string|max:255',
            'writer.bio' => 'nullable|string|max:500',
            'read_time' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
        ];

        // Only validate writer.avatar as image if a file is actually uploaded
        if ($request->hasFile('writer.avatar')) {
            $rules['writer.avatar'] = 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048';
        }

        $validated = $request->validate($rules);

        // Remove image from validated data if no new image is uploaded
        // This prevents trying to set image = null
        if (!$request->hasFile('image')) {
            unset($validated['image']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($article->image && Storage::disk('public')->exists(str_replace('/storage/', '', $article->image))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $article->image));
            }

            $imagePath = $request->file('image')->store('articles', 'public');
            $validated['image'] = '/storage/' . $imagePath;
        }

        // Handle author image upload
        if ($request->hasFile('writer.avatar')) {
            // Delete old author image
            if ($article->writer && isset($article->writer['avatar']) && Storage::disk('public')->exists(str_replace('/storage/', '', $article->writer['avatar']))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $article->writer['avatar']));
            }

            $avatarPath = $request->file('writer.avatar')->store('articles/authors', 'public');
            $validated['writer']['avatar'] = '/storage/' . $avatarPath;
        } else {
            // Preserve existing avatar if no new file is uploaded
            if ($article->writer && isset($article->writer['avatar'])) {
                $validated['writer']['avatar'] = $article->writer['avatar'];
            }
        }

        // Handle featured articles limit (max 2)
        if ($validated['is_featured'] && !$article->is_featured) {
            $featuredCount = Article::where('is_featured', true)->count();
            if ($featuredCount >= 2) {
                // Unfeature the oldest featured article
                Article::where('is_featured', true)
                    ->oldest()
                    ->first()
                    ->update(['is_featured' => false]);
            }
        }

        // Convert content string to array format for consistency
        if (isset($validated['content']) && is_string($validated['content'])) {
            $validated['content'] = $this->convertContentToArray($validated['content']);
        }

        // Update slug if title changed
        if ($validated['title'] !== $article->title) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $article->update($validated);

        return redirect()->route('admin.articles.index')->with('success', 'Article updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Article $article)
    {
        // Delete associated image
        if ($article->image && Storage::disk('public')->exists(str_replace('/storage/', '', $article->image))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $article->image));
        }

        $article->delete();

        return redirect()->route('admin.articles.index')->with('success', 'Article deleted successfully.');
    }

    /**
     * Convert content string to array format for consistent frontend handling
     */
    private function convertContentToArray($content)
    {
        if (empty($content)) {
            return [];
        }

        // Check if content is HTML (from TiptapEditor)
        if (strip_tags($content) !== $content) {
            return $this->convertHtmlToArray($content);
        }

        // Handle plain text content (legacy or manual input)
        return $this->convertPlainTextToArray($content);
    }

    /**
     * Convert HTML content from TiptapEditor to structured array
     */
    private function convertHtmlToArray($htmlContent)
    {
        $contentArray = [];

        // Create a DOMDocument to parse HTML
        $dom = new \DOMDocument();
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $htmlContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        foreach ($dom->childNodes as $node) {
            if ($node->nodeType === XML_TEXT_NODE) {
                $text = trim($node->textContent);
                if (!empty($text)) {
                    $contentArray[] = [
                        'type' => 'paragraph',
                        'content' => $text
                    ];
                }
                continue;
            }

            if ($node->nodeType !== XML_ELEMENT_NODE) {
                continue;
            }

            $tagName = strtolower($node->tagName);
            $textContent = trim($node->textContent);

            if (empty($textContent)) {
                continue;
            }

            switch ($tagName) {
                case 'h1':
                case 'h2':
                case 'h3':
                case 'h4':
                case 'h5':
                case 'h6':
                    $contentArray[] = [
                        'type' => 'heading',
                        'content' => $textContent
                    ];
                    break;

                case 'ul':
                case 'ol':
                    $listItems = [];
                    foreach ($node->childNodes as $listItem) {
                        if ($listItem->nodeType === XML_ELEMENT_NODE && strtolower($listItem->tagName) === 'li') {
                            $listItems[] = trim($listItem->textContent);
                        }
                    }
                    if (!empty($listItems)) {
                        $contentArray[] = [
                            'type' => 'list',
                            'content' => implode("\n", $listItems)
                        ];
                    }
                    break;

                case 'blockquote':
                    $contentArray[] = [
                        'type' => 'quote',
                        'content' => $textContent
                    ];
                    break;

                case 'p':
                default:
                    $contentArray[] = [
                        'type' => 'paragraph',
                        'content' => $textContent
                    ];
                    break;
            }
        }

        return $contentArray;
    }

    /**
     * Convert plain text content to structured array (legacy support)
     */
    private function convertPlainTextToArray($content)
    {
        // Split content by double newlines to create paragraphs
        $paragraphs = preg_split('/\n\s*\n/', trim($content));
        $contentArray = [];

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (empty($paragraph)) {
                continue;
            }

            // Check if it's a heading (starts with ##)
            if (preg_match('/^##\s+(.+)/', $paragraph, $matches)) {
                $contentArray[] = [
                    'type' => 'heading',
                    'content' => trim($matches[1])
                ];
            }
            // Check if it's a list (contains bullet points or numbered items)
            elseif (preg_match('/^[\s]*[-*•]\s+/', $paragraph) || preg_match('/^\d+\.\s+/', $paragraph)) {
                $contentArray[] = [
                    'type' => 'list',
                    'content' => $paragraph
                ];
            }
            // Check if it's a quote (starts with >)
            elseif (preg_match('/^>\s+(.+)/', $paragraph, $matches)) {
                $contentArray[] = [
                    'type' => 'quote',
                    'content' => trim($matches[1])
                ];
            }
            // Default to paragraph
            else {
                $contentArray[] = [
                    'type' => 'paragraph',
                    'content' => $paragraph
                ];
            }
        }

        return $contentArray;
    }
}
