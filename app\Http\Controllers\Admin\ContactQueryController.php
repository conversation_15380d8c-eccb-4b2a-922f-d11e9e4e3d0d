<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ContactQuery;
use App\Exports\ContactQueriesExport;
use Maatwebsite\Excel\Facades\Excel;

class ContactQueryController extends Controller
{
    public function index(Request $request)
    {
        $query = ContactQuery::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('company_name', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $contactQueries = $query->latest()->paginate(15);

        return inertia('Admin/ContactQueries/Index', [
            'contactQueries' => $contactQueries,
            'filters' => $request->only(['search', 'date_from', 'date_to']),
        ]);
    }

    public function export()
    {
        return Excel::download(new ContactQueriesExport, 'contact-queries.xlsx');
    }

    public function destroy(ContactQuery $contactQuery)
    {
        $contactQuery->delete();

        return redirect()->route('admin.contact-queries.index')->with('success', 'Contact query deleted successfully.');
    }

    public function bulkDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:contact_queries,id',
        ]);

        ContactQuery::whereIn('id', $request->ids)->delete();

        return redirect()->route('admin.contact-queries.index')->with('success', 'Selected contact queries deleted successfully.');
    }
}
