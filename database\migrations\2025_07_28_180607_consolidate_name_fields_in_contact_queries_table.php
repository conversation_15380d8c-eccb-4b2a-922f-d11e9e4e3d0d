<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add the new 'name' column
        Schema::table('contact_queries', function (Blueprint $table) {
            $table->string('name')->after('id');
        });

        // Step 2: Migrate existing data by concatenating first_name and last_name
        DB::table('contact_queries')->update([
            'name' => DB::raw("CONCAT(first_name, ' ', last_name)")
        ]);

        // Step 3: Drop the old columns
        Schema::table('contact_queries', function (Blueprint $table) {
            $table->dropColumn(['first_name', 'last_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Add back the old columns
        Schema::table('contact_queries', function (Blueprint $table) {
            $table->string('first_name')->after('id');
            $table->string('last_name')->after('first_name');
        });

        // Step 2: Split the name back into first_name and last_name
        // This is a best-effort approach - it splits on the first space
        $contactQueries = DB::table('contact_queries')->get();
        foreach ($contactQueries as $query) {
            $nameParts = explode(' ', $query->name, 2);
            $firstName = $nameParts[0] ?? '';
            $lastName = $nameParts[1] ?? '';

            DB::table('contact_queries')
                ->where('id', $query->id)
                ->update([
                    'first_name' => $firstName,
                    'last_name' => $lastName
                ]);
        }

        // Step 3: Drop the name column
        Schema::table('contact_queries', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
};
