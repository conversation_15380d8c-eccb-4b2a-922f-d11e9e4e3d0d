import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Link from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Placeholder from "@tiptap/extension-placeholder";
import { useEffect, useState } from "react";
import "../../css/tiptap.css";
import {
    TextBIcon,
    TextItalicIcon,
    TextUnderlineIcon,
    TextStrikethroughIcon,
    ListBulletsIcon,
    ListNumbersIcon,
    LinkIcon,
    TextAlignLeftIcon,
    TextAlignCenterIcon,
    TextAlignRightIcon,
    CodeIcon,
    QuotesIcon,
} from "@phosphor-icons/react";

const TiptapEditor = ({
    value = "",
    onChange,
    placeholder = "Write something...",
    style = {},
    className = "",
}) => {
    const [selectionVersion, setSelectionVersion] = useState(0);
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                heading: {
                    levels: [1, 2, 3, 4, 5, 6],
                },
            }),
            Link.configure({
                openOnClick: false,
                HTMLAttributes: {
                    class: "text-blue-600 underline",
                },
            }),
            TextAlign.configure({
                types: ["heading", "paragraph"],
            }),
            Underline,
            Placeholder.configure({
                placeholder: placeholder,
            }),
        ],
        content: value,
        onUpdate: ({ editor }) => {
            const html = editor.getHTML();
            if (onChange) {
                onChange(html);
            }
        },
        onSelectionUpdate: () => {
            setSelectionVersion((v) => v + 1);
        },
        editorProps: {
            attributes: {
                class: "prose prose-sm max-w-none focus:outline-none p-4",
            },
        },
    });

    // Update content when value prop changes
    useEffect(() => {
        if (editor && value !== editor.getHTML()) {
            editor.commands.setContent(value || "");
        }
    }, [value, editor]);

    if (!editor) {
        return null;
    }

    const ToolbarButton = ({ onClick, isActive, children, title }) => (
        <button
            type="button"
            onClick={onClick}
            className={`rounded p-2 transition-colors hover:bg-gray-100 ${
                isActive
                    ? "border border-blue-300 bg-blue-100 text-blue-900"
                    : "text-gray-600"
            }`}
            title={title}
        >
            {children}
        </button>
    );

    const ToolbarDivider = () => <div className="mx-1 h-6 w-px bg-gray-300" />;

    const handleHeadingChange = (level) => {
        if (!editor) return;
        editor.chain().focus();
        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        if (hasSelection) {
            // Replace selected text with heading/paragraph node
            const selectedText = editor.state.doc.textBetween(from, to);
            editor.commands.deleteRange({ from, to });
            if (level === 0) {
                editor.commands.insertContentAt(from, `<p>${selectedText}</p>`);
            } else {
                editor.commands.insertContentAt(
                    from,
                    `<h${level}>${selectedText}</h${level}>`,
                );
            }
            // Move selection inside the new node
            editor.commands.setTextSelection(from, from + selectedText.length);
        } else {
            // No selection: set block type for new text
            if (level === 0) {
                editor.chain().focus().setParagraph().run();
            } else {
                editor.chain().focus().setHeading({ level }).run();
            }
        }
    };

    const getCurrentHeadingLevel = () => {
        if (!editor) return 0;
        const { from, to } = editor.state.selection;
        let foundLevel = null;
        editor.state.doc.nodesBetween(from, to, (node) => {
            if (node.type.name === "heading") {
                if (foundLevel === null) foundLevel = node.attrs.level;
                else if (foundLevel !== node.attrs.level) foundLevel = 0;
            } else if (node.type.name !== "text") {
                if (foundLevel === null) foundLevel = 0;
            }
        });
        if (foundLevel !== null) return foundLevel;
        const { $anchor } = editor.state.selection;
        const currentNode = $anchor.parent;
        if (currentNode.type.name === "heading") {
            return currentNode.attrs.level;
        }
        for (let level = 1; level <= 6; level++) {
            if (editor.isActive("heading", { level })) {
                return level;
            }
        }
        return 0;
    };

    const handleTextFormatting = (command) => {
        if (!editor) return;

        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        // Focus the editor
        editor.chain().focus();

        if (hasSelection) {
            // Apply formatting to selected text only
            command();
        } else {
            // No selection - toggle the mark for future typing
            // This is the default behavior for most formatting commands
            command();
        }
    };

    const handleLinkInsertion = () => {
        if (!editor) return;

        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        if (hasSelection) {
            // If text is selected, apply link to selection
            const url = window.prompt("Enter URL:");
            if (url) {
                editor.chain().focus().setLink({ href: url }).run();
            }
        } else {
            // If no selection, prompt for both text and URL
            const linkText = window.prompt("Enter link text:");
            if (linkText) {
                const url = window.prompt("Enter URL:");
                if (url) {
                    editor
                        .chain()
                        .focus()
                        .insertContent(`<a href="${url}">${linkText}</a>`)
                        .run();
                }
            }
        }
    };

    const handleListToggle = (listType) => {
        if (!editor) return;

        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        editor.chain().focus();

        if (hasSelection) {
            // Apply list formatting to selected blocks
            if (listType === "bullet") {
                editor.chain().focus().toggleBulletList().run();
            } else if (listType === "ordered") {
                editor.chain().focus().toggleOrderedList().run();
            }
        } else {
            // Apply to current block
            if (listType === "bullet") {
                editor.chain().focus().toggleBulletList().run();
            } else if (listType === "ordered") {
                editor.chain().focus().toggleOrderedList().run();
            }
        }
    };

    const handleBlockquoteToggle = () => {
        if (!editor) return;

        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        editor.chain().focus();

        if (hasSelection) {
            // Apply blockquote to selected blocks
            editor.chain().focus().toggleBlockquote().run();
        } else {
            // Apply to current block
            editor.chain().focus().toggleBlockquote().run();
        }
    };

    const handleTextAlign = (alignment) => {
        if (!editor) return;

        const { from, to } = editor.state.selection;
        const hasSelection = from !== to;

        editor.chain().focus();

        if (hasSelection) {
            // Apply alignment to selected blocks
            editor.chain().focus().setTextAlign(alignment).run();
        } else {
            // Apply to current block
            editor.chain().focus().setTextAlign(alignment).run();
        }
    };

    return (
        <div
            className={`tiptap-editor rounded-md border border-gray-300 ${className}`}
            style={style}
        >
            {/* Toolbar */}
            <div className="flex flex-wrap items-center gap-1 border-b border-gray-300 bg-gray-50 p-2">
                {/* Headings */}
                <select
                    onChange={(e) => {
                        const level = parseInt(e.target.value);
                        handleHeadingChange(level);
                    }}
                    value={getCurrentHeadingLevel()}
                    className="rounded border border-gray-300 bg-white px-2 py-1 text-sm"
                >
                    <option value={0}>Paragraph</option>
                    <option value={1}>Heading 1</option>
                    <option value={2}>Heading 2</option>
                    <option value={3}>Heading 3</option>
                    <option value={4}>Heading 4</option>
                    <option value={5}>Heading 5</option>
                    <option value={6}>Heading 6</option>
                </select>

                <ToolbarDivider />

                {/* Text formatting */}
                <ToolbarButton
                    onClick={() =>
                        handleTextFormatting(() =>
                            editor.chain().focus().toggleBold().run(),
                        )
                    }
                    isActive={editor.isActive("bold")}
                    title="Bold"
                >
                    <TextBIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() =>
                        handleTextFormatting(() =>
                            editor.chain().focus().toggleItalic().run(),
                        )
                    }
                    isActive={editor.isActive("italic")}
                    title="Italic"
                >
                    <TextItalicIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() =>
                        handleTextFormatting(() =>
                            editor.chain().focus().toggleUnderline().run(),
                        )
                    }
                    isActive={editor.isActive("underline")}
                    title="Underline"
                >
                    <TextUnderlineIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() =>
                        handleTextFormatting(() =>
                            editor.chain().focus().toggleStrike().run(),
                        )
                    }
                    isActive={editor.isActive("strike")}
                    title="Strikethrough"
                >
                    <TextStrikethroughIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() =>
                        handleTextFormatting(() =>
                            editor.chain().focus().toggleCode().run(),
                        )
                    }
                    isActive={editor.isActive("code")}
                    title="Code"
                >
                    <CodeIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarDivider />

                {/* Lists */}
                <ToolbarButton
                    onClick={() => handleListToggle("bullet")}
                    isActive={editor.isActive("bulletList")}
                    title="Bullet List"
                >
                    <ListBulletsIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() => handleListToggle("ordered")}
                    isActive={editor.isActive("orderedList")}
                    title="Numbered List"
                >
                    <ListNumbersIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarDivider />

                {/* Blockquote */}
                <ToolbarButton
                    onClick={handleBlockquoteToggle}
                    isActive={editor.isActive("blockquote")}
                    title="Blockquote"
                >
                    <QuotesIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarDivider />

                {/* Text Alignment */}
                <ToolbarButton
                    onClick={() => handleTextAlign("left")}
                    isActive={editor.isActive({ textAlign: "left" })}
                    title="Align Left"
                >
                    <TextAlignLeftIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() => handleTextAlign("center")}
                    isActive={editor.isActive({ textAlign: "center" })}
                    title="Align Center"
                >
                    <TextAlignCenterIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                    onClick={() => handleTextAlign("right")}
                    isActive={editor.isActive({ textAlign: "right" })}
                    title="Align Right"
                >
                    <TextAlignRightIcon className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarDivider />

                {/* Link */}
                <ToolbarButton
                    onClick={handleLinkInsertion}
                    isActive={editor.isActive("link")}
                    title="Add Link"
                >
                    <LinkIcon className="h-4 w-4" />
                </ToolbarButton>

                {/* Horizontal Rule */}
                <ToolbarButton
                    onClick={() =>
                        editor.chain().focus().setHorizontalRule().run()
                    }
                    title="Horizontal Rule"
                >
                    <div className="flex h-4 w-4 items-center justify-center">
                        <div className="h-px w-3 bg-gray-600"></div>
                    </div>
                </ToolbarButton>
            </div>

            {/* Editor Content */}
            <div className="tiptap-content-wrapper">
                <EditorContent editor={editor} />
            </div>
        </div>
    );
};

export default TiptapEditor;
