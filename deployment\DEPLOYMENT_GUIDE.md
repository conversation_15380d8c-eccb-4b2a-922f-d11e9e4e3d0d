# Hostinger Deployment Checklist for www.pandapatronage.com

## Pre-Deployment (Local)

### 1. Install Production Dependencies
```bash
composer install --no-dev --optimize-autoloader --no-interaction
```

### 2. Build Frontend Assets
```bash
npm install
npm run build
```

### 3. Generate Application Key
```bash
php artisan key:generate --show
```
Copy this key and update it in your production .env file.

### 4. Clear Caches
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

### 5. Optimize for Production
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Files to Upload to Hostinger

### Upload Structure:
```
public_html/
├── app/
├── bootstrap/
├── config/
├── database/
├── public/           # Contents go directly to public_html/
├── resources/
├── routes/
├── storage/
├── vendor/
├── .env             # Use the production version
├── .htaccess        # Laravel routing (from public/.htaccess)
├── artisan
├── composer.json
├── composer.lock
└── create_storage_link.php
```

**Important for Shared Hosting**: Since you cannot change the document root from `public_html`, you'll need to:
1. Upload all Laravel core files to `public_html/`
2. Move contents of `public/` directory directly to `public_html/`
3. Update paths in `index.php` to reflect the new structure

### Files to Upload:
1. All Laravel files EXCEPT:
   - node_modules/
   - .env (use production version)
   - storage/logs/* (can be empty)
   - bootstrap/cache/* (will be regenerated)
   - public/ directory (contents will be moved to root)

2. Upload the production .env file as .env

3. Special handling for public/ directory:
   - Upload contents of public/ directly to public_html/ root
   - Use public.htaccess as .htaccess in public_html/
   - Update index.php to point to correct paths

## Post-Deployment (On Server)

### 1. Document Root (Shared Hosting Limitation)
- **Cannot change document root** in shared hosting - it's fixed to `public_html/`
- This is why we upload public/ contents directly to public_html/
- The uploaded index.php will handle routing from the root

### 2. Update .env File
- Update database credentials
- Update mail settings
- Verify APP_URL=https://www.pandapatronage.com

### 3. Set Permissions
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### 4. Create Storage Link
- Run: `php create_storage_link.php` (via file manager or SSH)
- Or access: https://www.pandapatronage.com/create_storage_link.php

### 5. Run Database Migrations
```bash
php artisan migrate --force
```

### 6. Clear and Cache
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Database Setup on Hostinger

1. Create MySQL database in Hostinger control panel
2. Note down:
   - Database name
   - Username
   - Password
   - Host (usually localhost)
3. Update these in your .env file

## SSL Certificate

Hostinger provides free SSL. Ensure:
1. SSL is enabled in control panel
2. Force HTTPS redirect is enabled
3. APP_URL uses https://

## Testing

1. Visit: https://www.pandapatronage.com
2. Check all pages load correctly
3. Test image uploads (storage link)
4. Test contact forms
5. Check admin panel access

## Troubleshooting

### Common Issues:

1. **500 Error**: Check file permissions and .env configuration
2. **Images not loading**: Run storage link script
3. **CSS/JS not loading**: Check build process and file permissions
4. **Database connection**: Verify credentials in .env

### Hostinger Specific:

1. PHP version: Ensure PHP 8.2+ is selected
2. File permissions: 755 for directories, 644 for files
3. .htaccess: Ensure mod_rewrite is enabled (usually is)

## Maintenance

- Keep Laravel and dependencies updated
- Regular database backups
- Monitor error logs
- Update SSL certificates (auto-renewed)

## Support

If issues persist:
1. Check Hostinger knowledge base
2. Contact Hostinger support
3. Check Laravel logs in storage/logs/
