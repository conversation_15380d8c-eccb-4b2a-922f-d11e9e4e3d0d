# Files and directories to EXCLUDE when uploading to server

## Directories to exclude:
- node_modules/
- .git/
- tests/
- deployment/

## Files to exclude:
- .env (use production version instead)
- .env.example
- README.md
- package-lock.json
- .gitignore
- .gitattributes
- phpunit.xml
- webpack.mix.js
- vite.config.js

## Logs (can be empty):
- storage/logs/*.log

## Cache (will be regenerated):
- bootstrap/cache/*.php
- storage/framework/cache/*
- storage/framework/sessions/*
- storage/framework/views/*

## Local development:
- fix_storage_link.php
- deploy_to_hostinger.php

## Note: 
Upload the vendor/ directory (with production dependencies only)
Upload the public/build/ directory (compiled assets)
