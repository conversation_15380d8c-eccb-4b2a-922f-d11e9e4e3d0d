import { useState, useEffect, useRef } from "react";
import { Link, usePage } from "@inertiajs/react";
import {
    HouseIcon,
    BriefcaseIcon,
    ChatCircleIcon,
    SignOutIcon,
    KeyIcon,
    CaretDownIcon,
    XIcon,
    ListIcon,
    ArticleIcon,
    TagIcon,
} from "@phosphor-icons/react";

const AdminLayout = ({ children }) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [userDropdownOpen, setUserDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target)
            ) {
                setUserDropdownOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    const { url, auth } = usePage().props;

    const navigation = [
        {
            name: "Dashboard",
            href: "/admin",
            icon: HouseIcon,
            current: url === "/admin",
        },
        {
            name: "Articles",
            href: "/admin/articles",
            icon: ArticleIcon,
            current: url && url.startsWith("/admin/articles"),
        },
        {
            name: "Blog Tags",
            href: "/admin/blog-tags",
            icon: TagIcon,
            current: url && url.startsWith("/admin/blog-tags"),
        },
        {
            name: "Cases",
            href: "/admin/cases",
            icon: BriefcaseIcon,
            current: url && url.startsWith("/admin/cases"),
        },
        {
            name: "Portfolio Tags",
            href: "/admin/portfolio-tags",
            icon: TagIcon,
            current: url && url.startsWith("/admin/portfolio-tags"),
        },
        {
            name: "Contact Queries",
            href: "/admin/contact-queries",
            icon: ChatCircleIcon,
            current: url && url.startsWith("/admin/contact-queries"),
        },
    ];

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Mobile sidebar */}
            <div
                className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? "block" : "hidden"}`}
            >
                <div
                    className="bg-opacity-75 fixed inset-0 bg-gray-600"
                    onClick={() => setSidebarOpen(false)}
                />
                <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
                    <div className="flex h-16 items-center justify-between px-4">
                        <h1 className="text-xl font-semibold text-gray-900">
                            Admin Panel
                        </h1>
                        <button
                            type="button"
                            className="text-gray-400 hover:text-gray-600"
                            onClick={() => setSidebarOpen(false)}
                        >
                            <XIcon className="h-6 w-6" />
                        </button>
                    </div>
                    <nav className="flex-1 space-y-1 px-2 py-4">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                onClick={() => setSidebarOpen(false)}
                                className={`group flex items-center rounded-md px-2 py-2 text-sm font-medium ${
                                    item.current
                                        ? "bg-gray-100 text-gray-900"
                                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                }`}
                            >
                                <item.icon className="mr-3 h-6 w-6 flex-shrink-0" />
                                {item.name}
                            </Link>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Desktop sidebar */}
            <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
                <div className="flex flex-grow flex-col overflow-y-auto border-r border-gray-200 bg-white pt-5 pb-4">
                    <div className="flex flex-shrink-0 items-center px-4">
                        <h1 className="text-xl font-semibold text-gray-900">
                            Admin Panel
                        </h1>
                    </div>
                    <nav className="mt-5 flex flex-1 flex-col divide-y divide-gray-200 overflow-y-auto">
                        <div className="space-y-1 px-2">
                            {navigation.map((item) => (
                                <Link
                                    key={item.name}
                                    href={item.href}
                                    className={`group flex items-center rounded-md px-2 py-2 text-sm font-medium ${
                                        item.current
                                            ? "bg-gray-100 text-gray-900"
                                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                    }`}
                                >
                                    <item.icon className="mr-3 h-6 w-6 flex-shrink-0" />
                                    {item.name}
                                </Link>
                            ))}
                        </div>
                    </nav>
                </div>
            </div>

            {/* Main content */}
            <div className="flex flex-1 flex-col lg:pl-64">
                {/* Top navigation */}
                <div className="sticky top-0 z-10 flex h-16 flex-shrink-0 bg-white shadow">
                    <button
                        type="button"
                        className="border-r border-gray-200 px-4 text-gray-500 focus:ring-2 focus:ring-indigo-500 focus:outline-none focus:ring-inset lg:hidden"
                        onClick={() => setSidebarOpen(true)}
                    >
                        <ListIcon className="h-6 w-6" />
                    </button>
                    <div className="flex flex-1 justify-between px-4">
                        <div className="flex flex-1">
                            {/* You can add search or other elements here */}
                        </div>
                        <div className="ml-4 flex items-center md:ml-6">
                            <div className="relative" ref={dropdownRef}>
                                <button
                                    onClick={() =>
                                        setUserDropdownOpen(!userDropdownOpen)
                                    }
                                    className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:text-gray-900 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                                >
                                    <span className="mr-2">
                                        Welcome, {auth?.user?.name}
                                    </span>
                                    <CaretDownIcon className="h-4 w-4" />
                                </button>

                                {userDropdownOpen && (
                                    <div className="ring-opacity-5 absolute right-0 z-50 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black">
                                        <div className="py-1">
                                            <Link
                                                href={route(
                                                    "admin.profile.password",
                                                )}
                                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() =>
                                                    setUserDropdownOpen(false)
                                                }
                                            >
                                                <KeyIcon className="mr-3 h-4 w-4" />
                                                Change Password
                                            </Link>
                                            <Link
                                                href="/admin/logout"
                                                method="post"
                                                as="button"
                                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() =>
                                                    setUserDropdownOpen(false)
                                                }
                                            >
                                                <SignOutIcon className="mr-3 h-4 w-4" />
                                                Logout
                                            </Link>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Page content */}
                <main className="flex-1">
                    <div className="py-6">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
                            {children}
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
};

export default AdminLayout;
