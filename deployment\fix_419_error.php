<?php
/**
 * Quick Fix for 419 CSRF "Page Expired" Error
 * Upload this to your server and run it once to fix session/cache issues
 */

echo "=== Fixing 419 CSRF Error ===\n\n";

// Step 1: Clear all caches
echo "1. Clearing caches...\n";

$cacheCommands = [
    'config:clear',
    'cache:clear', 
    'route:clear',
    'view:clear',
    'session:flush'
];

foreach ($cacheCommands as $command) {
    echo "   Running: php artisan $command\n";
    $output = shell_exec("php artisan $command 2>&1");
    if ($output) {
        echo "   Output: " . trim($output) . "\n";
    }
}

// Step 2: Set proper permissions
echo "\n2. Setting storage permissions...\n";
$storageDir = __DIR__ . '/storage';
$bootstrapCacheDir = __DIR__ . '/bootstrap/cache';

if (is_dir($storageDir)) {
    chmod($storageDir, 0755);
    
    // Set permissions recursively
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($storageDir),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $item) {
        if ($item->isDir()) {
            chmod($item, 0755);
        } else {
            chmod($item, 0644);
        }
    }
    echo "   ✓ Storage permissions set\n";
}

if (is_dir($bootstrapCacheDir)) {
    chmod($bootstrapCacheDir, 0755);
    echo "   ✓ Bootstrap cache permissions set\n";
}

// Step 3: Create session table if needed
echo "\n3. Checking session table...\n";
$output = shell_exec("php artisan session:table 2>&1");
if (strpos($output, 'already exists') === false) {
    echo "   Creating session table...\n";
    shell_exec("php artisan migrate --force 2>&1");
}
echo "   ✓ Session table ready\n";

// Step 4: Test CSRF token generation
echo "\n4. Testing CSRF token generation...\n";
try {
    // Start session to test
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Generate a test token
    if (function_exists('csrf_token')) {
        $token = csrf_token();
        echo "   ✓ CSRF token generated successfully\n";
    } else {
        echo "   ! CSRF function not available (normal for CLI)\n";
    }
} catch (Exception $e) {
    echo "   ! CSRF test error: " . $e->getMessage() . "\n";
}

// Step 5: Cache for production
echo "\n5. Caching for production...\n";
$cacheCommands = [
    'config:cache',
    'route:cache',
    'view:cache'
];

foreach ($cacheCommands as $command) {
    echo "   Running: php artisan $command\n";
    $output = shell_exec("php artisan $command 2>&1");
    if ($output) {
        echo "   Output: " . trim($output) . "\n";
    }
}

// Step 6: Environment check
echo "\n6. Environment verification...\n";

$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // Check key configurations
    $checks = [
        'APP_KEY=' => 'Application key',
        'APP_ENV=production' => 'Environment',
        'APP_DEBUG=false' => 'Debug mode',
        'SESSION_DRIVER=' => 'Session driver',
        'DB_CONNECTION=' => 'Database connection'
    ];
    
    foreach ($checks as $check => $description) {
        if (strpos($envContent, $check) !== false) {
            echo "   ✓ $description configured\n";
        } else {
            echo "   ✗ $description missing\n";
        }
    }
} else {
    echo "   ✗ .env file not found!\n";
}

echo "\n=== Fix Complete! ===\n";
echo "\nNext steps:\n";
echo "1. Try logging in again\n";
echo "2. Clear browser cache/cookies if still having issues\n";
echo "3. Check that your database connection is working\n";
echo "4. Verify APP_URL matches your domain exactly\n\n";

echo "If 419 error persists, check:\n";
echo "- Browser developer tools for JavaScript errors\n";
echo "- Laravel logs in storage/logs/ directory\n";
echo "- Session cookie settings in browser\n";
?>
